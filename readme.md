
# Caddy ja4 hash

This is a module for the Caddy web server that calculates the JA4 hash for each request.

## Installation
You can build with xcaddy:
```bash
xcaddy build --with github.com/bangnokia/caddy-ja4
```

## Usage

Add this to your Caddyfile:

```
route {
		ja4h
}
```

This will respond with a JSON object containing the JA4 hash for each request:

```
{
	"ja4": "...hash..."
}
```

Replace the placeholder hash logic in `ja4.go` with your own implementation as needed.

