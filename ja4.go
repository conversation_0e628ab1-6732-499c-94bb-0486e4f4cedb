package ja4


import (
	"encoding/json"
	"net/http"

	"github.com/caddyserver/caddy/v2"
	"github.com/caddyserver/caddy/v2/caddyconfig/caddyfile"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
	"github.com/lum8rjack/go-ja4h/ja4h"
)

func init() {
	caddy.RegisterModule(JA4Handler{})
}

// JA4Handler implements an HTTP handler that returns the JA4 hash as JSON.
type JA4Handler struct{}

// CaddyModule returns the Caddy module information.
func (JA4Handler) CaddyModule() caddy.ModuleInfo {
	return caddy.ModuleInfo{
		ID:  "http.handlers.ja4h",
		New: func() caddy.Module { return new(JA4Handler) },
	}
}

// Provision sets up the module. (No-op)
func (h *JA4Handler) Provision(ctx caddy.Context) error {
	return nil
}

// Validate ensures the module is properly configured. (No-op)
func (h *J<PERSON>4<PERSON>and<PERSON>) Validate() error {
	return nil
}

// ServeHTTP calculates the JA4 hash and writes it as JSON.
func (h JA4Handler) ServeHTTP(w http.ResponseWriter, r *http.Request, next caddyhttp.Handler) error {
	ja4 := calculateJA4(r)
	w.Header().Set("Content-Type", "application/json")
	return json.NewEncoder(w).Encode(map[string]string{"ja4": ja4})
}

// UnmarshalCaddyfile configures the handler from Caddyfile.
func (h *JA4Handler) UnmarshalCaddyfile(d *caddyfile.Dispenser) error {
	return nil
}

// calculateJA4 computes a basic JA4 hash from request properties.
func calculateJA4(r *http.Request) string {
	// Use go-ja4h to calculate the JA4 hash from the request
	hash := ja4h.Calculate(r)
	return hash
}

// Interface guards
var (
	_ caddy.Module      = (*JA4Handler)(nil)
	_ caddyhttp.Handler = (*JA4Handler)(nil)
)
